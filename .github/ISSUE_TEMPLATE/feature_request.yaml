name: "💡 Feature Request"
description: 创建新功能请求 | Create a new ticket for a new feature request
title: "💡 [REQUEST] - <title>"
labels: [
  "question"
]
body:
  - type: input
    id: start_date
    attributes:
      label: "起始日期 | Start Date"
      description: |
        起始开发日期
        Start of development
      placeholder: "month/day/year"
    validations:
      required: false
  - type: textarea
    id: implementation_pr
    attributes:
      label: "实现PR | Implementation PR"
      description: |
        实现该功能的Pull request
        Pull request used
      placeholder: "#Pull Request ID"
    validations:
      required: false
  - type: textarea
    id: reference_issues
    attributes:
      label: "相关Issues | Reference Issues"
      description: |
        与该功能相关的issues
        Common issues
      placeholder: "#Issues IDs"
    validations:
      required: false
  - type: textarea
    id: summary
    attributes:
      label: "摘要 | Summary"
      description: |
        简要描述新功能的特点
        Provide a brief explanation of the feature
      placeholder: |
        Describe in a few lines your feature request
    validations:
      required: true
  - type: textarea
    id: basic_example
    attributes:
      label: "基本示例 | Basic Example"
      description: Indicate here some basic examples of your feature.
      placeholder: A few specific words about your feature request.
    validations:
      required: true
  - type: textarea
    id: drawbacks
    attributes:
      label: "缺陷 | Drawbacks"
      description: |
        该新功能有哪些缺陷/可能造成哪些影响？
        What are the drawbacks/impacts of your feature request ?
      placeholder: |
        Identify the drawbacks and impacts while being neutral on your feature request
    validations:
      required: true
  - type: textarea
    id: unresolved_question
    attributes:
      label: "未解决问题 | Unresolved questions"
      description: |
        有哪些尚未解决的问题？
        What questions still remain unresolved ?
      placeholder: |
        Identify any unresolved issues.
    validations:
      required: false