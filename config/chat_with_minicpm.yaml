default:
  logger:
    log_level: "INFO"
  service:
    host: "0.0.0.0"
    port: 8282
    cert_file: "ssl_certs/localhost.crt"
    cert_key: "ssl_certs/localhost.key"
  chat_engine:
    model_root: "models"
    handler_search_path:
      - "src/handlers"
    handler_configs:
      RtcClient:
        module: client/rtc_client/client_handler_rtc
      SileroVad:
        module: vad/silerovad/vad_handler_silero
        speaking_threshold: 0.5
        start_delay: 2048
        end_delay: 5000
        buffer_look_back: 5000
        speech_padding: 512
      MiniCPM-o:
        enabled: True
        module: llm/minicpm/llm_handler_minicpm
        model_name: "MiniCPM-o-2_6"
        # model_name: "MiniCPM-o-2_6-int4"
        voice_prompt: "你是一个AI助手。你能接受视频，音频和文本输入并输出语音和文本。模仿输入音频中的声音特征。"
        assistant_prompt: "作为助手，你将使用这种声音风格说话。"
        enable_video_input: True
        skip_video_frame: 2
      LiteAvatar:
        module: avatar/liteavatar/avatar_handler_liteavatar
        avatar_name: 20250408/sample_data
        fps: 25
        debug: false
        enable_fast_mode: false
        use_gpu: false
