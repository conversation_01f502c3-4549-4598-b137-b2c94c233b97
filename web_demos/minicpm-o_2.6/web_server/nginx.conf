user root;
worker_processes auto;
pid /run/nginx.pid;
include /etc/nginx/modules-enabled/*.conf;

events {
	worker_connections 768;
	# multi_accept on;
}

http {

	##
	# Basic Settings
	##

	client_max_body_size 20M;

	sendfile on;
	tcp_nopush on;
	tcp_nodelay on;
	keepalive_timeout 65;
	types_hash_max_size 2048;
	# server_tokens off;

	# server_names_hash_bucket_size 64;
	# server_name_in_redirect off;

	include /etc/nginx/mime.types;
	default_type application/octet-stream;

	##
	# SSL Settings
	##

	ssl_protocols TLSv1 TLSv1.1 TLSv1.2 TLSv1.3; # Dropping SSLv3, ref: POODLE
	ssl_prefer_server_ciphers on;

	##
	# Logging Settings
	##

	access_log /var/log/nginx/access.log;
	error_log /var/log/nginx/error.log;

	##
	# Gzip Settings
	##

	gzip on;

	# gzip_vary on;
	# gzip_proxied any;
	# gzip_comp_level 6;
	# gzip_buffers 16 8k;
	# gzip_http_version 1.1;
	# gzip_types text/plain text/css application/json application/javascript text/xml application/xml application/xml+rss text/javascript;

    ##
	# Virtual Host Configs
	##
	server {
		#	listen 8080;
		server_name localhost;

		add_header Access-Control-Allow-Origin *;
		add_header Access-Control-Allow-Headers X-Requested-With;
		add_header Access-Control-Allow-Methods GET,POST,OPTIONS;

        # 后端请求
        location /api/v1 {
            proxy_pass http://127.0.0.1:32550;
            proxy_set_header Host $host;
            proxy_set_header Connection "";
            chunked_transfer_encoding off;
            proxy_set_header X-Accel-Buffering off;  # 这里设置X-Accel-Buffering头部
            add_header X-Accel-Buffering off;         # 这里是用于响应中显示X-Accel-Buffering头部
            proxy_http_version 1.1;
            # 关闭 Nginx 缓存
            proxy_buffering off;
            proxy_cache off;
            # 禁用 Nginx 默认缓冲条件
            sendfile off;
            tcp_nodelay on;
        }
        location /ws {
            proxy_pass http://127.0.0.1:32550;
            proxy_http_version 1.1;
            proxy_set_header Upgrade $http_upgrade;
            proxy_set_header Connection 'upgrade';
            proxy_set_header Host $host;
            proxy_cache_bypass $http_upgrade;
        }
		location / {
			root /usr/share/nginx/html;

			index index.html index.htm;
			try_files $uri $uri/ /index.html;
		}

		location @router {
			rewrite ^.*$ /index.html last;
		}

		location =/robots.txt {
			index robots.txt;
		}

	}
}
